<?php

namespace App\Http\Controllers\Admin;

use App\Code\External\Api\SalesforceMarketingApi;
use App\Http\Controllers\Controller;
use App\Http\Models\Api\Address;
use App\Http\Models\Api\Country;
use App\Http\Models\Api\Title;
use App\Http\Models\Api\User;
use App\Http\Models\SiteLogs;
use App\Http\Requests\UserSaveRequest;
use DB;
use Exception;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Excel\UsersExport;

class UserController extends Controller{
    /**
     * UserController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request){
        $this->middleware('ipcheck');
        $this->middleware('logincheck');
    }

    /**
     * @param Request $request
     *
     * @return Factory|View
     */
    public function clubMembers(Request $request){

        $data['active_menu']    = 'users';
        $data['active_submenu'] = 'club-members';

        return view('admin.club-members', $data);
    }

    /**
     * @param Request $request
     *
     * @return JSON
     */
    public function ajaxclubMembersList(Request $request){
        ini_set('max_execution_time', '1200');
        ini_set('memory_limit', '4096M');

        $draw   = intval($_GET["draw"]);
        $start  = intval($_GET["start"]);
        $length = intval($_GET["length"]);
        $search = $_GET["search"];
        $order  = $_GET["order"];

        $search_val = "";
        if(isset($search['value']) && $search['value'] != ""){
            $search_val = sanitizeStr($search['value']);
        }

        if($search_val) {
            $all_users = User::groupBy("member_id")->get();
            $search_val_lower = strtolower($search_val);
            Log::info(__METHOD__ . " - Search value: " . $search_val_lower . " through " . $all_users->count() . " users");
            $filtered_users = $all_users->filter(function($user) use ($search_val_lower) {
                return
                    (stripos($user->first_name, $search_val_lower) !== false) ||
                    (stripos($user->last_name, $search_val_lower) !== false) ||
                    (stripos($user->email, $search_val_lower) !== false) ||
                    (stripos($user->member_id, $search_val_lower) !== false) ||
                    (stripos($user->salesforce_id, $search_val_lower) !== false);
            });
            $total_count = $filtered_users->count();
        } else {
            $total_count = User::groupBy("member_id")->count();
            $filtered_users = User::groupBy("member_id");
        }

        //get order by
        $sort_cols = array(0 => "member_id", 1 => "status", 2 => "first_name", 3 => "last_name", 4 => "email", 5 => "day_of_birth");
        if(in_array($order[0]['column'], array_keys($sort_cols))){
            $sort_col   = $order[0]['column'];
            $sort_order = $order[0]['dir'];
        }

        // Handle sorting and pagination differently based on whether we're searching or not
        if($search_val) {
            // For search results, we need to sort and paginate the collection manually
            $sorted_users = $filtered_users->sortBy(function($user) use ($sort_cols, $sort_col, $sort_order) {
                return $user->{$sort_cols[$sort_col]};
            }, SORT_REGULAR, $sort_order === 'desc');

            // Apply pagination manually to the collection
            $users = $sorted_users->slice($start, $length)->values();
        } else {
            // For no search, use the query builder's efficient sorting and pagination
            $users = $filtered_users
                ->orderBy($sort_cols[$sort_col], $sort_order)
                ->skip($start)
                ->take($length)
                ->get();
        }


        //table data
        $data = array();
        foreach($users as $user){

            $edit_link = '<a href="' . route('admin_user_edit', ["id" => $user->id]) . '">Edit</a>';

            //delete competition
            /*$delete_form = ' &nbsp;|&nbsp; <form action="' . route('admin_user_remove') . '" method="post" class="list-delete-form" data-msg="Are you sure to delete this user?">';
            $delete_form .= csrf_field() . method_field('DELETE');
            $delete_form .= '<input type="hidden" name="user_id" value="' . $user->id . '">';
            $delete_form .= '<button type="submit" class="btn-delete">Delete</button>';
            $delete_form .= '</form>';*/

            $data[] = array(
                $user->member_id,
                $user->status,
                $user->first_name,
                $user->last_name,
                $user->email,
                $user->day_of_birth,
                $edit_link,    // . $delete_form
            );
        }

        $output = array(
            "draw"            => $draw,
            "recordsTotal"    => $total_count,
            "recordsFiltered" => $total_count,
            "data"            => $data
        );
        echo json_encode($output);
    }


    /**
     * @param Request $request
     * @param         $id
     *
     * @return Factory|View
     */
    public function edit(Request $request, $id){
        /* @var User $user */
        $user = User::where('id', $id)->first();
        if($user){
            $data["user_id"] = $user->id;
            $data["user"]    = $user->toArray();

            $address         = Address::where('user_id', $user->id)->first();
            $data["address"] = $address->toArray();
            $data["purchases"] = $user->purchases()->get()->toArray();
        }
        else{
            return redirect()->route("admin_user_club_members");
        }

        //save title
        $titleOptions = array();
        $titles       = Title::get();
        foreach($titles as $title){
            $titleOptions[$title->full_name] = $title->short_name;
        }
        $data['titleOptions'] = $titleOptions;

        //save country
        $countryOptions = array();
        $countries      = Country::get();
        foreach($countries as $country){
            $countryOptions[$country->name] = $country->id;
        }
        $data['countryOptions'] = $countryOptions;

        $data['active_menu']    = 'users';
        $data['active_submenu'] = 'club-members';

        return view('admin.user_form', $data);
    }

    /**
     * @param UserSaveRequest $request
     * @param int             $id
     *
     * @return RedirectResponse
     */
    public function save(UserSaveRequest $request, $id){
        /* @var User $user */
        if($id) $user = User::where('id', $id)->first();

        if($user){
            //save user data
            $user->title       = sanitizeStr($request->input("title"));
            $user->title_after = sanitizeStr($request->input("title_after"));
            //$user->salutation        = sanitizeStr($request->input("salutation"));
            $user->app_is_downloaded = $request->input("app_is_downloaded") ? 1 : 0;
            $user->first_name        = sanitizeStr($request->input("first_name"));
            $user->last_name         = sanitizeStr($request->input("last_name"));
            $user->gender            = sanitizeStr($request->input("gender"));
            $user->email             = sanitizeStr($request->input("email"));
            $user->mobile            = sanitizeStr($request->input("mobile"));
            $user->day_of_birth      = sanitizeStr($request->input("day_of_birth")) ? date('Y-m-d', strtotime($request->input("day_of_birth"))) : "";
            $user->country_id        = sanitizeStr($request->input("country_id"));

            if(isDevhost() || isLocalhost()){
                $changePassword = sanitizeStr($request->input("change_password"));
                if($changePassword || (string)$changePassword === "0"){
                    if((string)$changePassword === "0"){
                        $user->password = "";

                        // Flash change infos for user
                        appendFlashSessionChangelog("app.backend.user_changed_info", "User password removed!");
                    }
                    else{
                        $user->password = Hash::make($changePassword);

                        // Flash change infos for user
                        appendFlashSessionChangelog("app.backend.user_changed_info", "User password changed!");
                    }
                }
            }

            $user->save();

            //save address data
            $address = Address::where('user_id', $user->id)->first();
            if(!$address){
                $address          = new Address();
                $address->user_id = $user->id;
            }

            $address->street            = sanitizeStr($request->input("street"));
            $address->street_number     = sanitizeStr($request->input("street_number"));
            $address->street_additional = sanitizeStr($request->input("street_additional"));
            $address->post_code         = sanitizeStr($request->input("post_code"));
            $address->city              = sanitizeStr($request->input("city"));
            $address->save();

            // Flash change infos for user
            appendFlashSessionChangelog("app.backend.user_changed_info", "User updated!");

            // Fetch country
            /* @var Country $country */
            $country = Country::where("id", $user->country_id)->first();

            // Create new user with salesforce api
            if(!$user->test_user){

                $responseData = app(SalesforceMarketingApi::class)->createOrUpdateAccount([
                    "salesforceId"             => $user->salesforce_id,
                    "title"                    => $user->title,
                    "titleAfter"               => $user->title_after,
                    "salutation"               => $user->salutation ?: "",
                    "firstName"                => $user->first_name,
                    "lastName"                 => $user->last_name,
                    "email"                    => $user->email,
                    "gender"                   => M(["m" => "MALE", "f" => "FEMALE", "d" => "UNKNOWN"], $user->gender),
                    "mobile"                   => $user->mobile,
                    "birthdate"                => date("Y-m-d", strtotime($user->day_of_birth)),
                    "street"                   => getSfStreet($address->street, $address->street_number, $address->street_additional),
                    "postalCode"               => $address->post_code,
                    "city"                     => $address->city,
                    "country"                  => $country->getLanguageCode(),
                    "personHasOptedOutOfEmail" => false,
                    "marketingNewsOptOut"      => !$request->input("newsletter_sub") ? false : true,
                    "nlClubNewsOptOut"         => !$request->input("club_newsletter_sub") ? false : true,
                    "isAppDownloaded"          => $user->app_is_downloaded ? true : false,
                    "isEmployee"               => $user->is_employee ? true : false,
                    "pushAllowed"              => $user->push_allowed ? true : false,
                ]);

                $rawResponse = $responseData;
                if(isset($responseData["accountInfo"]) && $responseData["accountInfo"])
                    $responseData = $responseData["accountInfo"];

                // Save additional user data
                if(isset($responseData["salesforceId"])){
                    $user->salesforce_id = $responseData["salesforceId"];
                    $user->member_id     = $responseData["cardNumber"];
                    $user->status        = $responseData["accountStatus"] ?? ($responseData["status"] ?? "");

                    if(isset($responseData["personContactId"])) $user->person_contact_id = $responseData["personContactId"];
                    if(isset($responseData["titleAfter"])) $user->title_after = $responseData["titleAfter"];
                    if(isset($responseData["title"])) $user->title = $responseData["title"];
                    if(isset($responseData["salutation"])) $user->salutation = $responseData["salutation"];
                    if(isset($responseData["nextLevelOfMembership"])) $user->next_status = $responseData["nextLevelOfMembership"];
                    if(isset($responseData["isEmployee"])) $user->is_employee = $responseData["isEmployee"];
                    if(isset($responseData["pushAllowed"])) $user->push_allowed = $responseData["pushAllowed"];
                    if(isset($responseData["amountToNextLevel"])) $user->next_status_amount = $responseData["amountToNextLevel"];
                    if(isset($responseData["statusExpirationDate"])) $user->status_expire_at = date("Y-m-d H:i:s", @strtotime($responseData["statusExpirationDate"]));
                    if(isset($responseData["statusDiscount"])) $user->status_discount = $responseData["statusDiscount"];
                    if(isset($responseData["accountStatusDiscount"])) $user->status_discount = $responseData["accountStatusDiscount"];
                    if(isset($responseData["percentToNextLevel"])) $user->next_status_percent = $responseData["percentToNextLevel"];
                    if(isset($responseData["sourceOfRegistration"])) $user->source_of_registration = $responseData["sourceOfRegistration"];
                    if(isset($responseData["registrationLanguage"])) $user->registration_language = $responseData["registrationLanguage"];

                    if(isset($rawResponse["changeFromPOS"])) $user->change_from_pos = $rawResponse["changeFromPOS"] ? true : false;

                    $user->save();

                    // Flash change infos for user
                    appendFlashSessionChangelog("app.backend.user_changed_info", "Salesforce synced!");
                }
                else{
                    // Flash change infos for user
                    appendFlashSessionChangelog("app.backend.user_changed_info", "Salesforce user not found!");
                }
            }
        }

        SiteLogs::addFromRequest($request, shortClassMethod(__METHOD__), ["id" => $id]);
        return redirect()->route("admin_user_club_members");
    }

    /**
     * @param UserSaveRequest $request
     * @param int             $id
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function remove(Request $request, $id){
        /* @var User $user */
        $user = null;
        if($id) $user = User::where('id', $id)->first();
        if($user){
            // Flash change infos for user
            appendFlashSessionChangelog("app.backend.user_changed_info", "User '" . $user->member_id . "' has been removed from the middleware!");
            $user->delete();
        }

        SiteLogs::addFromRequest($request, shortClassMethod(__METHOD__), ["id" => $id]);
        return redirect()->route("admin_user_club_members");
    }


    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function exportUsers(Request $request){
        ini_set('memory_limit', '4096M');

        SiteLogs::addFromRequest($request, shortClassMethod(__METHOD__));

        return Excel::download(new UsersExport(), "all_users_" . date('d-m-Y') . ".xlsx");
    }
}
